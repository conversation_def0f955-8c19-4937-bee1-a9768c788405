# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesKeywordCuration(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_keyword_curation(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-关键词管理-查询同关键词管理页面数据"""
        keyword_top_list = CentralIm().search_keyword_top_list(headers=sales_header, page=1, pageSize=20)

        assert keyword_top_list["result"] == True, f'查询同关键词管理页面数据异常{keyword_top_list}'
        assert len(keyword_top_list["object"]["data"]) > 0, f'查询同关键词管理页面data为空{keyword_top_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_keyword_curation_by_keyword(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-关键词管理-根据关键词查询"""
        keyword_top_list = CentralIm().search_keyword_top_list(headers=sales_header, keyword="test", page=1, pageSize=20)

        assert keyword_top_list["result"] == True, f'根据关键词查询关键词管理页面数据异常{keyword_top_list}'
        assert len(keyword_top_list["object"]["data"]) > 0, f'根据关键词查询关键词管理页面data为空，请修改关键词{keyword_top_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_keyword_curation_by_status(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-关键词管理-根据状态查询"""
        # 定义两种状态
        status_list = ["A", "X"]

        # 循环查询每种状态
        for status in status_list:
            keyword_top_list = CentralIm().search_keyword_top_list(headers=sales_header, status=status, page=1, pageSize=20)

            assert keyword_top_list["result"] == True, f'根据状态{status}查询关键词管理页面数据异常{keyword_top_list}'
            assert len(keyword_top_list["object"]["data"]) > 0, f'根据状态{status}查询关键词管理页面data为空{keyword_top_list}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_synonyms')
    def test_sales_keyword_curation_by_product_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-关键词管理-根据商品ID查询"""
        keyword_top_list = CentralIm().search_keyword_top_list(headers=sales_header, product_id="4", page=1, pageSize=20)

        assert keyword_top_list["result"] == True, f'根据商品ID查询关键词管理页面数据异常{keyword_top_list}'
        assert len(keyword_top_list["object"]["data"]) > 0, f'根据商品ID查询关键词管理页面data为空，请修改商品id{keyword_top_list}'

