# !/usr/bin/python3
# -*- coding: utf-8 -*-


import weeeTest

from test_dir.api.ec.central_portal.central_im import CentralIm


class TestSalesProductGroup(weeeTest.TestCase):

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_product_group')
    def test_sales_product_group(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-商品组-查询商品组页面数据"""
        product_group = CentralIm().group_list(headers=sales_header)

        assert len(product_group["object"]["data"]) > 0, f'查询商品组页面数据异常{product_group}'
        for index, item in enumerate(product_group["object"]["data"]) :
            group_id = item["group_id"]
            product_list = ["group_id"]
            # 商品销售信息管理-商品内容信息管理-商品组-查询商品组详情页面
            group_product_detail = CentralIm().group_product_detail(headers=sales_header, group_id=group_id)

            assert group_product_detail["object"][
                       "group_id"] == group_id, f'查询商品组详情group_id{group_id}页面数据异常{group_product_detail}'
            assert len(group_product_detail["object"][
                           "groupProductList"]) > 0, f'查询商品组详情group_id{group_id}页面数据异常{group_product_detail}'
            assert len(group_product_detail["object"][
                           "propertyList"]) > 0, f'查询商品组详情group_id{group_id}页面数据异常{group_product_detail}'
            product_ids = [item["product_id"] for item in group_product_detail["object"]["groupProductList"]]
            property_ids = [item["property_id"] for item in group_product_detail["object"]["propertyList"]]
            # 商品销售信息管理/商品内容信息管理/商品组详情商品check
            group_product_check = CentralIm().group_product_check(headers=sales_header,
                                                                  group_id=group_id,
                                                                  product_ids=product_ids,
                                                                  property_ids=property_ids
                                                                  )

            assert len(group_product_check[
                           "object"]) > 0, f'查询商品组详情check 接口group_id{group_id}页面数据异常{group_product_check}'

            if index == 2:
                break


    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_product_group')
    def test_sales_group_product_property(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-商品组-查询商品组属性"""
        group_product_property = CentralIm().group_product_property(headers=sales_header)

        assert len(group_product_property["object"]) > 0, f'查询商品组页面数据异常{group_product_property}'
        for item in (group_product_property["object"]):
            property_id = item["property_id"]
            group_property_value = CentralIm().group_property_value(headers=sales_header, property_ids=property_id)
            assert len(group_property_value["object"]) > 0, f'查询商品组页面数据异常{group_property_value}'

    @weeeTest.mark.list('sales', 'Transaction', 'test_sales_product_group')
    def test_sales_group_list_with_product_id(self, sales_header):
        """ # 商品销售信息管理-商品内容信息管理-商品组-根据product_id查询商品组列表"""
        # 测试必填参数pageSize
        group_list_pagesize = CentralIm().group_list(headers=sales_header, pageSize=10)
        assert group_list_pagesize["result"] == True, f'查询商品组列表pageSize参数测试异常{group_list_pagesize}'
        assert "data" in group_list_pagesize["object"], f'查询商品组列表pageSize参数测试返回数据异常{group_list_pagesize}'

        # 测试必填参数startColumn
        group_list_startcolumn = CentralIm().group_list(headers=sales_header, startColumn=0)
        assert group_list_startcolumn["result"] == True, f'查询商品组列表startColumn参数测试异常{group_list_startcolumn}'
        assert "data" in group_list_startcolumn["object"], f'查询商品组列表startColumn参数测试返回数据异常{group_list_startcolumn}'

        # 测试product_id查询条件
        group_list_product_id = CentralIm().group_list(headers=sales_header, product_id="95361", pageSize=20, startColumn=0)
        assert group_list_product_id["result"] == True, f'查询商品组列表product_id参数测试异常{group_list_product_id}'
        assert "data" in group_list_product_id["object"], f'查询商品组列表product_id参数测试返回数据异常{group_list_product_id}'
